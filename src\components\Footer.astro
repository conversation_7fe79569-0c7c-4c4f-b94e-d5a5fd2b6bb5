---
import SocialLinks from './SocialLinks.astro';

const currentYear = new Date().getFullYear();
---

<footer role="contentinfo" class="bg-dark text-white py-12">
  <div class="container mx-auto px-5 max-w-6xl">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-10 items-start">
      <div class="footer-info">
        <h3 class="text-lg font-semibold mb-4 font-heading">Nob Hokleng</h3>
        <p class="text-white/80 text-sm leading-relaxed mb-4">
          Software Developer & System Architect specializing in scalable backend systems and modern web applications.
        </p>
        <p class="text-white/60 text-xs">&copy; {currentYear} Nob Hokleng. All rights reserved.</p>
      </div>
      
      <div class="footer-navigation">
        <h4 class="text-base font-semibold mb-4 text-white">Quick Links</h4>
        <nav aria-label="Footer navigation">
          <ul class="space-y-2">
            <li><a href="/#about" class="footer-link text-white/80 text-sm font-medium transition-colors duration-300 hover:text-white relative" aria-label="Go to About section">About</a></li>
            <li><a href="/#portfolio" class="footer-link text-white/80 text-sm font-medium transition-colors duration-300 hover:text-white relative" aria-label="Go to Portfolio section">Portfolio</a></li>
            <li><a href="/resume" class="footer-link text-white/80 text-sm font-medium transition-colors duration-300 hover:text-white relative" aria-label="View Resume">Resume</a></li>
            <li><a href="/resources" class="footer-link text-white/80 text-sm font-medium transition-colors duration-300 hover:text-white relative" aria-label="View Resources">Resources</a></li>
            <li><a href="/contact" class="footer-link text-white/80 text-sm font-medium transition-colors duration-300 hover:text-white relative" aria-label="Go to Contact section">Contact</a></li>
            <li><a href="/rss.xml" class="footer-link text-white/80 text-sm font-medium transition-colors duration-300 hover:text-white relative" aria-label="Subscribe to RSS feed">RSS Feed</a></li>
          </ul>
        </nav>
      </div>
      
      <div class="footer-social">
        <h4 class="text-base font-semibold mb-4 text-white">Connect</h4>
        <div class="space-y-3">
          <a href="https://linkedin.com/in/nobhokleng" target="_blank" rel="noopener" class="flex items-center gap-3 text-white/80 hover:text-white transition-colors text-sm">
            <i class="fab fa-linkedin w-5"></i>
            <span>LinkedIn</span>
          </a>
          <a href="https://github.com/Nobhokleng" target="_blank" rel="noopener" class="flex items-center gap-3 text-white/80 hover:text-white transition-colors text-sm">
            <i class="fab fa-github w-5"></i>
            <span>GitHub</span>
          </a>
          <a href="mailto:<EMAIL>" class="flex items-center gap-3 text-white/80 hover:text-white transition-colors text-sm">
            <i class="fas fa-envelope w-5"></i>
            <span>Email</span>
          </a>
        </div>
        <div class="mt-6 pt-4 border-t border-white/20">
          <p class="text-white/60 text-xs">Currently available for new opportunities</p>
        </div>
      </div>
    </div>
    
    <div class="mt-8 pt-6 border-t border-white/20 text-center">
      <p class="text-white/60 text-xs">
        🚧 This site is currently under development. Expected completion: July 2025
      </p>
    </div>
  </div>
</footer>

<style>
.footer-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3a86ff, #ff9e00);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.footer-link:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}
</style> 